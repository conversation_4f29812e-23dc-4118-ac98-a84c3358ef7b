import { useState, useCallback, useEffect, useMemo } from 'react';
import { useAuth } from 'react-oidc-context';
import ReviewForm, { ReviewData } from '../../../../feature-module/components/ReviewForm/ReviewForm';
import RescheduleForm, { RescheduleData } from '../../../../feature-module/components/RescheduleForm/RescheduleForm';
import { useNavigate } from 'react-router-dom';
import { Spinner, Card, Chip, Pagination, Button } from '@heroui/react';
import { FaCalendarAlt } from 'react-icons/fa';
import { MapPin, Star } from 'lucide-react';
import { apiClient } from '../../../../api';
import { getImageUrlWithFallback } from '../aws/s3FileUpload';
import { createReview, CreateReviewData } from '../../../../service/reviewService';
import {
  getUserBookings,
  confirmBooking,
  finishBooking,
  cancelBooking,
  getBookingById,
  Booking as BookingType
} from '../../../../service/bookingService';
import { toast } from 'react-toastify';

// User data interface
interface UserData {
  name?: string;
  username?: string;
  email?: string;
  profileImage?: string;
}

const BookingList = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [showRescheduleForm, setShowRescheduleForm] = useState(false);
  const [showBookingDetails, setShowBookingDetails] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [bookingDetails, setBookingDetails] = useState<BookingType | null>(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [userData, setUserData] = useState<UserData>({});
  const [userDataLoading, setUserDataLoading] = useState(false);
  const [bookingsLoading, setBookingsLoading] = useState(false);
  const [bookingsError, setBookingsError] = useState<string | null>(null);


  // Derived media for Booking Details modal
  const detailsImages = useMemo(() => {
    const bd: any = bookingDetails as any;
    if (!bd) return [] as string[];
    let imgs: string[] = Array.isArray(bd.serviceImages) ? bd.serviceImages.filter((x: any) => !!x) : [];
    // Support alternative shapes like gallery: [{ serviceImages: string[] }]
    if (!imgs.length && Array.isArray(bd.gallery)) {
      imgs = bd.gallery
        .flatMap((g: any) => (Array.isArray(g?.serviceImages) ? g.serviceImages : []))
        .filter((x: any) => !!x);
    }
    // Normalize each entry to a full URL if it looks like a name (no http)
    imgs = imgs.map((val: string) => {
      if (!val) return val;
      if (val.startsWith('http')) return val;
      return getImageUrlWithFallback(val, 'service-images');
    });
    return imgs;
  }, [bookingDetails]);

  const detailsVideoLink = useMemo(() => {
    const bd: any = bookingDetails as any;
    if (!bd) return undefined as string | undefined;
    if (typeof bd.videoLink === 'string' && bd.videoLink) return bd.videoLink;
    if (Array.isArray(bd.serviceVideo) && bd.serviceVideo.length > 0) return bd.serviceVideo[0];
    return undefined;
  }, [bookingDetails]);


  // Initial bookings data (fallback when API is not available)
  const initialBookings: Booking[] = useMemo(() => [
    {
      id: 1,
      service: 'Computer Services',
      status: 'Cancelled' as const,
      date: '27 Sep 2022, 17:00-18:00',
      amount: '$100.00',
      payment: 'PayPal',
      location: 'Newark, USA',
      provider: 'John Doe',
      email: '<EMAIL>',
      phone: '******-888-8888',
      actions: ['View Details', 'Reschedule'],
      providerId: 'provider-1',
      bookingId: 'booking-1',
    },
    {
      id: 2,
      service: 'Car Repair Services',
      status: 'Completed' as const,
      date: '23 Sep 2022, 10:00-11:00',
      amount: '$50.00',
      payment: 'COD',
      location: 'Alabama, USA',
      provider: 'John Smith',
      email: '<EMAIL>',
      phone: '******-275-5393',
      actions: ['View Details', 'Rebook', 'Add Review'],
      providerId: 'provider-2',
      bookingId: 'booking-2',
    },
    {
      id: 3,
      service: 'Interior Designing',
      status: 'Inprogress' as const,
      date: '22 Sep 2022, 11:00-12:00',
      amount: '$50.00',
      payment: 'PayPal',
      location: 'Washington, DC, USA',
      provider: 'Quentin',
      email: '<EMAIL>',
      phone: '******-810-9218',
      actions: ['View Details', 'Chat', 'Cancel'],
      providerId: 'provider-3',
      bookingId: 'booking-3',
    },
  ], []);

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user) {
      return auth.user.profile.preferred_username ||
             auth.user.profile.sub ||
             auth.user.profile.email;
    }
    return null;
  }, [auth.user]);



  // Fetch user data from API
  const fetchUserData = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch user data: No user ID or not authenticated');
      return;
    }

    try {
      setUserDataLoading(true);
      console.log(`Fetching user data for ID: ${uid}`);

      const response = await apiClient.get(`/api/v1/user/${uid}`);

      if (response.data) {
        console.log('User data fetched successfully:', response.data);
        setUserData(response.data);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Fallback to auth profile data
      if (auth.user) {
        const fallbackData: UserData = {
          name: auth.user.profile.name ||
                auth.user.profile.given_name ||
                auth.user.profile.preferred_username ||
                auth.user.profile.email?.split('@')[0] ||
                'User',
          email: auth.user.profile.email,
          profileImage: 'https://via.placeholder.com/40'
        };
        setUserData(fallbackData);
      }
    } finally {
      setUserDataLoading(false);
    }
  }, [getUserId, auth.isAuthenticated, auth.user]);

  // Fetch bookings data from API
  const fetchBookings = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch bookings: No user ID or not authenticated');
      return;
    }

    try {
      setBookingsLoading(true);
      setBookingsError(null);
      console.log(`Fetching bookings for user ID: ${uid}`);

      const response = await getUserBookings({ userId: uid });

      if (response && response.bookings) {
        console.log('Bookings fetched successfully:', response.bookings);
        console.log('Sample booking data structure:', response.bookings[0]);

        // Debug: Check what booking ID fields are available
        if (response.bookings[0]) {
          console.log('First booking ID fields:', {
            id: response.bookings[0].id,
            bookingId: response.bookings[0].bookingId,
            referenceCode: response.bookings[0].referenceCode
          });
        }

        // Transform API data to match component interface
        const transformedBookings = response.bookings.map((booking: BookingType) => {
          // Extract images from gallery array if available
          const serviceImages = (() => {
            const anyBooking = booking as any;
            if (anyBooking.gallery && Array.isArray(anyBooking.gallery) && anyBooking.gallery.length > 0) {
              const galleryImages = anyBooking.gallery
                .flatMap((g: { serviceImages?: string[] }) => Array.isArray(g?.serviceImages) ? g.serviceImages : [])
                .filter((img: string) => !!img);
              return galleryImages.length > 0 ? galleryImages : (booking.serviceImages || []);
            }
            return booking.serviceImages || [];
          })();

          const b: Booking = {
            id: booking.id,
            service: booking.serviceName || booking.service,
            serviceName: booking.serviceName || booking.service,
            status: booking.status as Booking['status'],
            date: booking.date as string,
            amount: booking.amount as string,
            payment: (booking.paymentMethod || booking.payment) as string,
            paymentMethod: (booking.paymentMethod || booking.payment) as string,
            location: booking.location as string,
            provider: booking.provider as string,
            email: booking.email as string,
            phone: booking.phone as string,
            providerId: booking.providerId,
            bookingId: (booking.bookingId || booking.referenceCode || booking.id.toString()),
            serviceId: booking.serviceId,
            serviceImages,
            description: booking.description,
            duration: booking.duration,
            category: booking.category,
            personalInfo: booking.personalInfo,
            serviceDetails: booking.serviceDetails,
            referenceCode: booking.referenceCode,
            additionalServices: booking.additionalServices || [],
            appointmentTimeFrom: booking.appointmentTimeFrom,
            appointmentTimeTo: booking.appointmentTimeTo,
            appointmentDate: booking.appointmentDate,
            bookingStatus: booking.bookingStatus,
            gallery: (booking as any).gallery || [],
            actions: []
          };

          return { ...b, actions: getActionsForBooking(b) };
        });
        setBookings(transformedBookings);
      } else {
        console.log('No bookings found, using fallback data');
        setBookings(initialBookings);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setBookingsError('Failed to load bookings. Please try again.');
      // Fallback to initial bookings on error
      setBookings(initialBookings);
    } finally {
      setBookingsLoading(false);
    }
  }, [getUserId, auth.isAuthenticated, initialBookings]);

  // Determine if a booking is eligible for reschedule (only for the relevant service)
  const canReschedule = (booking: Booking): boolean => {
    const statusAllows = ['Confirmed', 'Inprogress', 'Pending'].includes(booking.status);
    const hasProvider = !!booking.providerId;

    // Optional: only allow future appointments to be rescheduled
    let isFuture = true;
    if (booking.appointmentDate) {
      const d = new Date(booking.appointmentDate);
      if (!isNaN(d.getTime())) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        d.setHours(0, 0, 0, 0);
        isFuture = d >= today;
      }
    }

    return statusAllows && hasProvider && isFuture;
  };

  // Helper to determine actions based on the whole booking (only show Reschedule for eligible services)
  const getActionsForBooking = (booking: Booking): string[] => {
    const baseActions = ['View Details'];
    switch (booking.status) {
      case 'Pending':
        return [...baseActions, 'Confirm', ...(canReschedule(booking) ? ['Reschedule'] : []), 'Cancel'];
      case 'Confirmed':
        return [...baseActions, 'Add Review', ...(canReschedule(booking) ? ['Reschedule'] : []), 'Cancel'];
      case 'Finished':
        return [...baseActions, 'Add Review', 'Rebook'];
      case 'Cancelled':
        return [...baseActions]; // No reschedule for cancelled bookings
      case 'Completed':
        return [...baseActions, 'Add Review', 'Rebook'];
      case 'Inprogress':
        return [...baseActions, ...(canReschedule(booking) ? ['Reschedule'] : []), 'Chat', 'Cancel'];
      case 'Rescheduled':
        return [...baseActions, 'Add to Calendar'];
      default:
        return [...baseActions, 'Chat'];
    }
  };

  // Fetch user data and bookings when component mounts
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchUserData();
      fetchBookings();
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchUserData, fetchBookings]);

  // State to manage bookings
  const [bookings, setBookings] = useState<Booking[]>(initialBookings);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4;

  // Calculate pagination
  const totalPages = Math.ceil(bookings.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentBookings = bookings.slice(startIndex, endIndex);

  type ButtonColor = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  type ButtonVariant = 'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost';

  // Define a type for booking (updated to match service interface)
  interface Booking {
    id: number | string;
    service: string;
    serviceName?: string; // Alternative field name for service
    status: 'Pending' | 'Confirmed' | 'Finished' | 'Cancelled' | 'Completed' | 'Inprogress' | 'Rescheduled';
    date: string;
    amount: string;
    payment: string;
    paymentMethod?: string; // Alternative field name for payment
    location: string;
    provider: string;
    email: string;
    phone: string;
    actions: string[];
    providerId?: string;
    bookingId?: string;
    serviceId?: string;
    serviceImages?: string[]; // Array of service image URLs
    description?: string; // Service description
    duration?: string; // Service duration
    category?: string; // Service category
    personalInfo?: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
      streetAddress?: string;
      city?: string;
      state?: string;
      postalCode?: string;
      notes?: string;
      address?: {
        street?: string;
        city?: string;
        state?: string;
        postalCode?: string;
        country?: string;
      };
    };
    serviceDetails?: {
      serviceTitle?: string;
      categoryId?: string;
      subCategoryId?: string;
      price?: number;
      isOffers?: boolean;
      isAdditional?: boolean;
      staff?: unknown;
      availableDate?: unknown;
    };
    referenceCode?: string; // Booking reference code
    additionalServices?: {
      id?: string;
      name?: string;
      price?: number;
      duration?: string;
      description?: string;
    }[]; // Array of additional services
    appointmentTimeFrom?: string; // Appointment start time
    appointmentTimeTo?: string; // Appointment end time
    appointmentDate?: string; // Appointment date
    bookingStatus?: string; // Detailed booking status information
    gallery?: {
      id?: string;
      serviceImages?: string[];
      videoLink?: string;
    }[]; // Gallery array containing service images and videos
  }

  // Function to handle opening the review form
  const handleAddReview = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowReviewForm(true);
  };

  // Function to handle submitting a review
  const handleSubmitReview = async (reviewData: ReviewData) => {
    if (!selectedBooking) {
      console.error('No booking selected for review');
      return;
    }

    try {
      console.log('Review submitted:', reviewData);
      console.log('Image data in review:', {
        imageCount: reviewData.imageNames?.length || 0,
        imageNames: reviewData.imageNames,
        imageUrls: reviewData.imageUrls
      });

      // Prepare review data for API
      const createData: CreateReviewData = {
        providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
        serviceId: selectedBooking.id.toString(),
        serviceName: selectedBooking.service,
        bookingId: selectedBooking.bookingId || `booking-${selectedBooking.id}`,
        title: reviewData.title || selectedBooking.service,
        review: reviewData.review,
        comment: reviewData.review, // Backend expects comment field
        rating: reviewData.rating,
        images: reviewData.images,
        imageUrls: reviewData.imageUrls || [],
        imageNames: reviewData.imageNames || [], // Include image names for backend storage
      };

      console.log('Prepared review data for API:', {
        ...createData,
        imageNamesCount: createData.imageNames?.length || 0,
        imageNames: createData.imageNames,
        ratingFields: {
          rating: createData.rating,
          serviceRating: createData.serviceRating,
          qualityRating: createData.qualityRating,
          valueRating: createData.valueRating,
          communicationRating: createData.communicationRating,
          timelinessRating: createData.timelinessRating
        }
      });

      // Backend requires all rating fields - provide defaults using overall rating if not set
      createData.serviceRating = (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4)
        ? Math.round(reviewData.serviceRating)
        : Math.round(reviewData.rating);

      createData.qualityRating = (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4)
        ? Math.round(reviewData.qualityRating)
        : Math.round(reviewData.rating);

      createData.valueRating = (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4)
        ? Math.round(reviewData.valueRating)
        : Math.round(reviewData.rating);

      createData.communicationRating = (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4)
        ? Math.round(reviewData.communicationRating)
        : Math.round(reviewData.rating);

      createData.timelinessRating = (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4)
        ? Math.round(reviewData.timelinessRating)
        : Math.round(reviewData.rating);

      // Submit review to API
      await createReview(createData);

      // Update booking status to "Finished" after successful review submission
      if (selectedBooking.bookingId) {
        try {
          await finishBooking(selectedBooking.bookingId);
          console.log('Booking status updated to Finished');

          // Update local booking state
          setBookings(prevBookings =>
            prevBookings.map(booking => {
              if (booking.id === selectedBooking.id) {
                const updated: Booking = { ...booking, status: 'Finished' };
                return { ...updated, actions: getActionsForBooking(updated) };
              }
              return booking;
            })
          );
        } catch (statusError) {
          console.error('Error updating booking status:', statusError);
          // Don't fail the review submission if status update fails
        }
      }

      // Show success message
      toast.success('Review submitted successfully!');

      // Close the review form
      setShowReviewForm(false);
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
    }
  };

  // Function to handle rescheduling
  const handleReschedule = (booking: Booking) => {
    console.log('Reschedule button clicked for booking:', booking);
    console.log('Booking ID fields:', {
      id: booking.id,
      bookingId: booking.bookingId,
      referenceCode: booking.referenceCode
    });
    setSelectedBooking(booking);
    setShowRescheduleForm(true);
  };

  // Function to handle rescheduling submission
  const handleRescheduleSubmit = (rescheduleData: RescheduleData) => {
    try {
      // In a real app, you would send this data to your backend
      console.log('Reschedule data submitted:', rescheduleData);

      // Parse time range (e.g., "10:00 AM-10:30 AM")
      const timeParts = (rescheduleData.newTime || '').split('-').map((s) => s.trim());
      const newTimeFrom = timeParts[0] || rescheduleData.newTime;
      const newTimeTo = timeParts[1] || undefined;

      // Format the date for display
      const dateOptions: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      const formattedDate = rescheduleData.newDate.toLocaleDateString('en-US', dateOptions);
      const newDateString = `${formattedDate}, ${rescheduleData.newTime}`;

      // Update the bookings list
      const updatedBookings = bookings.map((booking) => {
        if (booking.id === rescheduleData.bookingId) {
          const newBooking: Booking = {
            ...booking,
            date: newDateString,
            appointmentDate: formattedDate,
            appointmentTimeFrom: newTimeFrom,
            appointmentTimeTo: newTimeTo,
            status: 'Rescheduled',
          };
          return { ...newBooking, actions: getActionsForBooking(newBooking) };
        }
        return booking;
      });

      setBookings(updatedBookings);

      // Update currently selected booking and details if open
      const updated = updatedBookings.find((b) => b.id === rescheduleData.bookingId);
      if (updated) {
        setSelectedBooking(updated);
      }

      if (bookingDetails && (bookingDetails.id === rescheduleData.bookingId || bookingDetails.bookingId === selectedBooking?.bookingId)) {
        setBookingDetails({
          ...bookingDetails,
          date: newDateString,
          appointmentDate: formattedDate,
          appointmentTimeFrom: newTimeFrom,
          appointmentTimeTo: newTimeTo,
          status: 'Rescheduled',
          updatedAt: new Date().toISOString(),
        } as BookingType);
      }

      // Success message is shown by RescheduleForm
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      alert('Failed to reschedule booking. Please try again.');
    }
  };

  // Function to handle confirming a booking
  const handleConfirmBooking = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    try {
      await confirmBooking(booking.bookingId);

      // Update local booking state
      setBookings(prevBookings =>
        prevBookings.map(b => {
          if (b.id === booking.id) {
            const updated: Booking = { ...b, status: 'Confirmed' };
            return { ...updated, actions: getActionsForBooking(updated) };
          }
          return b;
        })
      );

      toast.success('Booking confirmed successfully!');
    } catch (error) {
      console.error('Error confirming booking:', error);
      toast.error('Failed to confirm booking. Please try again.');
    }
  };

  // Function to handle cancelling a booking
  const handleCancelBooking = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    if (window.confirm('Are you sure you want to cancel this booking?')) {
      try {
        await cancelBooking(booking.bookingId);

        // Update local booking state
        setBookings(prevBookings =>
          prevBookings.map(b => {
            if (b.id === booking.id) {
              const updated: Booking = { ...b, status: 'Cancelled' };
              return { ...updated, actions: getActionsForBooking(updated) };
            }
            return b;
          })
        );

        toast.success('Booking cancelled successfully!');
      } catch (error) {
        console.error('Error cancelling booking:', error);
        toast.error('Failed to cancel booking. Please try again.');
      }
    }
  };

  // Function to handle viewing booking details
  const handleViewDetails = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    try {
      setDetailsLoading(true);
      setSelectedBooking(booking);
      console.log('Fetching details for booking:', booking.bookingId);

      // Fetch detailed booking information from API
      const details = await getBookingById(booking.bookingId);

      // Log the complete booking details structure
      console.log('=== BOOKING DETAILS FETCHED ===');
      console.log('Full booking details:', details);
      console.log('Personal info:', details.personalInfo);
      console.log('Service details:', details.serviceDetails);
      console.log('Service images:', details.serviceImages);
      console.log('Payment method:', details.paymentMethod || details.payment);
      console.log('Booking status:', details.status);
      console.log('Reference code:', details.referenceCode);
      console.log('Additional services:', details.additionalServices);
      console.log('User address:', details.personalInfo?.address);
      console.log('Appointment time from:', details.appointmentTimeFrom);
      console.log('Appointment time to:', details.appointmentTimeTo);
      console.log('Appointment date:', details.appointmentDate);
      console.log('Booking status:', details.bookingStatus);
      console.log('Provider info:', {
        provider: details.provider,
        email: details.email,
        phone: details.phone,
        providerId: details.providerId
      });
      console.log('=== END BOOKING DETAILS ===');

      // Merge the fetched details with existing booking data to ensure all fields are available
      const enrichedDetails = {
        ...booking, // Start with existing booking data
        ...details, // Override with fetched details
        // Ensure critical fields are available
        serviceName: details.serviceName || details.service || booking.service,
        paymentMethod: details.paymentMethod || details.payment || booking.payment,
        serviceImages: (() => {
          // Extract images from gallery array if available
          if ((details as any).gallery && Array.isArray((details as any).gallery) && (details as any).gallery.length > 0) {
            const galleryImages = (details as any).gallery
              .flatMap((g: { serviceImages?: string[] }) => Array.isArray(g?.serviceImages) ? g.serviceImages : [])
              .filter((img: string) => !!img);
            return galleryImages.length > 0 ? galleryImages : (details.serviceImages || booking.serviceImages || []);
          }
          return details.serviceImages || booking.serviceImages || [];
        })(),
        gallery: (details as any).gallery || (booking as any).gallery || [],
      };

      setBookingDetails(enrichedDetails);
      setShowBookingDetails(true);

      // Show success message
      toast.success('Booking details loaded successfully!');
    } catch (error) {
      console.error('Error fetching booking details:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          toast.error('Booking not found. It may have been deleted.');
        } else if (error.message.includes('403')) {
          toast.error('You do not have permission to view this booking.');
        } else if (error.message.includes('500')) {
          toast.error('Server error. Please try again later.');
        } else {
          toast.error(`Failed to load booking details: ${error.message}`);
        }
      } else {
        toast.error('Failed to load booking details. Please try again.');
      }

      // Fallback: show modal with existing booking data if API fails
      console.log('Using fallback booking data:', booking);
      setBookingDetails(booking as BookingType);
      setShowBookingDetails(true);
    } finally {
      setDetailsLoading(false);
    }
  };

  // Function to handle button actions
  const handleButtonAction = (action: string, booking: Booking) => {
    switch (action) {
      case 'View Details':
        handleViewDetails(booking);
        break;
      case 'Confirm':
        handleConfirmBooking(booking);
        break;
      case 'Review':
      case 'Add Review':
        handleAddReview(booking);
        break;
      case 'Chat':
        navigate('/customer/chat');
        break;
      case 'Reschedule':
        handleReschedule(booking);
        break;
      case 'Cancel':
        handleCancelBooking(booking);
        break;
      case 'Rebook':
        // Handle rebook action
        alert('Rebook functionality will be implemented soon');
        break;
      case 'Add to Calendar': {
        // Create calendar event URL (works with Google Calendar)
        const eventTitle = encodeURIComponent(`Appointment: ${booking.service}`);
        const eventDetails = encodeURIComponent(`Provider: ${booking.provider}\nLocation: ${booking.location}\nContact: ${booking.email}, ${booking.phone}`);
        const eventLocation = encodeURIComponent(booking.location);

        // Parse date and time
        const [datePart, timePart] = booking.date.split(',');
        const dateObj = new Date(datePart);

        // Extract start and end times
        let startTime = '', endTime = '';
        if (timePart) {
          const timeRange = timePart.trim();
          const [start, end] = timeRange.split('-');
          startTime = start.trim();
          endTime = end ? end.trim() : '';
        }

        // Create start and end date objects
        const startDate = new Date(dateObj);
        const endDate = new Date(dateObj);

        // Set hours based on time string (simple parsing)
        if (startTime) {
          const [hourMin, period] = startTime.split(' ');
          let hour;
          const minute = hourMin.split(':').map(Number)[1] || 0;
          hour = Number(hourMin.split(':')[0]);
          if (period === 'PM' && hour < 12) hour += 12;
          if (period === 'AM' && hour === 12) hour = 0;
          startDate.setHours(hour, minute, 0);

          // Default end time is 1 hour later if not specified
          if (endTime) {
            const [endHourMin, endPeriod] = endTime.split(' ');
            let endHour;
            const endMinute = endHourMin.split(':').map(Number)[1] || 0;
            endHour = Number(endHourMin.split(':')[0]);
            if (endPeriod === 'PM' && endHour < 12) endHour += 12;
            if (endPeriod === 'AM' && endHour === 12) endHour = 0;
            endDate.setHours(endHour, endMinute, 0);
          } else {
            endDate.setHours(startDate.getHours() + 1, startDate.getMinutes(), 0);
          }
        }

        // Format dates for URL
        const formatDateForCalendar = (date: Date) => {
          return date.toISOString().replace(/-|:|\.\d+/g, '');
        };

        const startDateStr = formatDateForCalendar(startDate);
        const endDateStr = formatDateForCalendar(endDate);

        // Create Google Calendar URL
        const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${eventTitle}&details=${eventDetails}&location=${eventLocation}&dates=${startDateStr}/${endDateStr}`;

        // Open in new window
        window.open(calendarUrl, '_blank');
        break;
      }

      default:
        break;
    }
  };

  const getButtonStyles = (action: string): { color: ButtonColor; variant: ButtonVariant } => {
    switch (action) {
      case 'View Details':
        return {
          color: 'default',
          variant: 'bordered'
        };
      case 'Confirm':
        return {
          color: 'success',
          variant: 'solid'
        };
      case 'Review':
        return {
          color: 'primary',
          variant: 'solid'
        };
      case 'Cancel':
        return {
          color: 'danger',
          variant: 'light'
        };
      case 'Chat':
        return {
          color: 'success',
          variant: 'bordered'
        };
      case 'Reschedule':
        return {
          color: 'warning',
          variant: 'bordered'
        };
      case 'Add Review':
        return {
          color: 'secondary',
          variant: 'bordered'
        };
      case 'Rebook':
        return {
          color: 'primary',
          variant: 'solid'
        };
      case 'Add to Calendar':
        return {
          color: 'success',
          variant: 'flat'
        };
      default:
        return {
          color: 'primary',
          variant: 'bordered'
        };
    }
  };

  return (
    <div className="mx-auto p-4">
      {/* <BreadCrumb title="My Bookings" item1="Customer" /> */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 mt-4">
        {/* <h2 className="text-3xl font-bold text-gray-800 mb-4 sm:mb-0">My Bookings</h2> */}
        {!bookingsLoading && !bookingsError && bookings.length > 0 && (
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
              Total: {bookings.length}
            </span>
            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
              Completed: {bookings.filter(b => b.status === 'Completed' || b.status === 'Finished').length}
            </span>
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full font-medium">
              Pending: {bookings.filter(b => b.status === 'Pending').length}
            </span>
            <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">
              Confirmed: {bookings.filter(b => b.status === 'Confirmed').length}
            </span>
          </div>
        )}
      </div>



      {/* Loading State */}
      {bookingsLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="text-lg text-gray-600">Loading bookings...</div>
        </div>
      )}

      {/* Error State */}
      {bookingsError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{bookingsError}</p>
          <button
            onClick={fetchBookings}
            className="mt-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
          >
            Retry
          </button>
        </div>
      )}

      {/* Bookings List */}
      {!bookingsLoading && !bookingsError && (
        <>
          {bookings.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
                <FaCalendarAlt className="text-gray-400 text-3xl" />
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No Bookings Found</h3>
              <p className="text-gray-500 mb-6">You haven't made any bookings yet.</p>
              <button
                onClick={() => window.location.href = '/'}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Browse Services
              </button>
            </div>
          ) : (
            <>
              <div className="space-y-8">
                {currentBookings.map((booking) => (
                  <Card key={booking.id} className="flex flex-row items-center w-full max-w-4xl shadow-md rounded-2xl overflow-hidden">
                    {/* Left Image */}
                    <div className="w-40 h-40 overflow-hidden">
                      {booking.serviceImages && booking.serviceImages.length > 0 ? (
                        <img
                          src={booking.serviceImages[0].startsWith('http') ? booking.serviceImages[0] : getImageUrlWithFallback(booking.serviceImages[0], 'service-images')}
                          alt={booking.serviceName || booking.service}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <img
                          src="https://via.placeholder.com/150"
                          alt="Service"
                          className="object-cover w-full h-full"
                        />
                      )}
                    </div>

                    {/* Right Content */}
                    <div className="flex flex-1 justify-between items-center p-6">
                      {/* Service Details */}
                      <div className="flex flex-col gap-2">
                        <span className="px-2 py-1 text-xs bg-gray-100 rounded-md w-fit">
                          {booking.category || 'Service'}
                        </span>
                        <h3 className="text-lg font-semibold">
                          {booking.serviceName || booking.service}
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <MapPin size={16} />
                          <span>{booking.location}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-yellow-500">
                          <Star size={16} fill="gold" />
                          <span>4.9</span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Chip
                            size="sm"
                            color={
                              booking.status === 'Cancelled' ? 'danger' :
                              booking.status === 'Completed' || booking.status === 'Finished' ? 'success' :
                              booking.status === 'Confirmed' ? 'primary' :
                              booking.status === 'Pending' ? 'warning' :
                              booking.status === 'Inprogress' ? 'secondary' :
                              booking.status === 'Rescheduled' ? 'warning' :
                              'default'
                            }
                            variant="flat"
                          >
                            {booking.status}
                          </Chip>
                        </div>
                      </div>

                      {/* Price & Button */}
                      <div className="flex flex-col items-end gap-2">
                        <span className="text-lg font-semibold text-gray-800">{booking.amount}</span>
                        <div className="flex gap-2">
                          <Button
                            color="primary"
                            radius="md"
                            size="sm"
                            onPress={() => handleButtonAction('View Details', booking)}
                          >
                            View Details
                          </Button>
                          {booking.actions.includes('Add Review') && (
                            <Button
                              color="secondary"
                              variant="bordered"
                              radius="md"
                              size="sm"
                              onPress={() => handleButtonAction('Add Review', booking)}
                            >
                              Add Review
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <Pagination
                    isCompact
                    showControls
                    page={currentPage}
                    total={totalPages}
                    onChange={setCurrentPage}
                  />
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Review Form Modal */}
      {selectedBooking && showReviewForm && (
        <ReviewForm
          isOpen={showReviewForm}
          onClose={() => setShowReviewForm(false)}
          onSubmit={handleSubmitReview}
          providerId={selectedBooking.providerId || `provider-${selectedBooking.id}`}
          serviceId={selectedBooking.id.toString()}
          bookingId={selectedBooking.bookingId || `booking-${selectedBooking.id}`}
          serviceName={selectedBooking.service}
          initialData={{
            id: `review-${Date.now()}`,
            providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
            serviceId: selectedBooking.id.toString(),
            serviceName: selectedBooking.service,
            rating: 0,
            title: `Review for ${selectedBooking.service}`,
            review: '',
            images: [],
            imageUrls: [],
            imageNames: [] // Include empty image names array
          }}
        />
      )}

      {/* Reschedule Form Modal */}
      {selectedBooking && showRescheduleForm && (
        <RescheduleForm
          isOpen={showRescheduleForm}
          onClose={() => setShowRescheduleForm(false)}
          onSubmit={handleRescheduleSubmit}
          bookingDetails={{
            id: typeof selectedBooking.id === 'string' ? parseInt(selectedBooking.id) : selectedBooking.id,
            service: selectedBooking.service,
            date: selectedBooking.date,
            provider: selectedBooking.provider,
            bookingId: selectedBooking.bookingId || selectedBooking.referenceCode || String(selectedBooking.id),
            providerId: selectedBooking.providerId
          }}
        />
      )}

      {/* Booking Details Slide-in Panel */}
      {showBookingDetails && (
        <div className="fixed inset-0 z-50">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/40"
            onClick={() => {
              setShowBookingDetails(false);
              setBookingDetails(null);
              setSelectedBooking(null);
            }}
          />

          {/* Panel */}
          <div className={`absolute right-0 top-0 h-screen w-full max-w-md bg-white shadow-2xl transform transition-transform duration-300 translate-x-0`}>
            {/* Header */}
            <div className="bg-indigo-600 px-4 py-3 flex items-center justify-between gap-2">
              <div className="flex items-center gap-2">
                <h2 className="text-base font-semibold text-white">Booking Confirmation</h2>
                {bookingDetails && (
                  <span className={`text-white text-[10px] font-medium px-2 py-0.5 rounded-full ${
                    bookingDetails.status === 'Confirmed' || bookingDetails.status === 'Completed' ? 'bg-green-500/20' :
                    bookingDetails.status === 'Pending' ? 'bg-yellow-500/20' :
                    bookingDetails.status === 'Cancelled' ? 'bg-red-500/20' :
                    'bg-white/20'
                  }`}>
                    {bookingDetails.status}
                  </span>
                )}
              </div>
              <button
                aria-label="Close"
                className="text-white/90 hover:text-white text-xl leading-none"
                onClick={() => {
                  setShowBookingDetails(false);
                  setBookingDetails(null);
                  setSelectedBooking(null);
                }}
              >
                &times;
              </button>
            </div>

            {/* Body */}
            <div className="h-[calc(100vh-48px)] overflow-y-auto p-4">
              {detailsLoading ? (
                <div className="flex items-center justify-center h-full">
                  <Spinner size="lg" />
                  <span className="ml-3 text-sm text-slate-600">Loading booking details...</span>
                </div>
              ) : bookingDetails ? (
                <div className="space-y-4">
                  {/* Top Summary */}
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <p className="text-slate-500 text-[11px] font-medium">Booking #</p>
                      <p className="text-slate-900 text-sm font-medium mt-1 break-all">#{bookingDetails.referenceCode || selectedBooking?.bookingId || selectedBooking?.id}</p>
                    </div>
                    <div>
                      <p className="text-slate-500 text-[11px] font-medium">Date</p>
                      <p className="text-slate-900 text-sm font-medium mt-1">{bookingDetails.appointmentDate || bookingDetails.date || (bookingDetails.createdAt ? new Date(bookingDetails.createdAt).toLocaleDateString() : 'N/A')}</p>
                    </div>
                    <div>
                      <p className="text-slate-500 text-[11px] font-medium">Total</p>
                      <p className="text-indigo-700 text-sm font-semibold mt-1">{bookingDetails.amount}</p>
                    </div>
                  </div>

                  {/* Service Info */}
                  <div className="bg-gray-50 rounded-md p-3">
                    <h3 className="text-[13px] font-semibold text-slate-900 mb-3">Service Information</h3>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <p className="text-slate-500 text-[11px]">Service</p>
                        <p className="text-slate-900 mt-1">{bookingDetails.serviceName || bookingDetails.service}</p>
                      </div>
                      <div>
                        <p className="text-slate-500 text-[11px]">Provider</p>
                        <p className="text-slate-900 mt-1">{bookingDetails.provider}</p>
                      </div>
                      <div>
                        <p className="text-slate-500 text-[11px]">Location</p>
                        <p className="text-slate-900 mt-1">{bookingDetails.location}</p>
                      </div>
                      <div>
                        <p className="text-slate-500 text-[11px]">Contact</p>
                        <p className="text-slate-900 mt-1">{bookingDetails.phone}</p>
                      </div>
                    </div>
                  </div>

                  {/* Customer Info */}
                  {bookingDetails.personalInfo && (
                    <div className="bg-gray-50 rounded-md p-3">
                      <h3 className="text-[13px] font-semibold text-slate-900 mb-3">Customer Information</h3>
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        {(bookingDetails.personalInfo.firstName || bookingDetails.personalInfo.lastName) && (
                          <div>
                            <p className="text-slate-500 text-[11px]">Customer</p>
                            <p className="text-slate-900 mt-1">{bookingDetails.personalInfo.firstName} {bookingDetails.personalInfo.lastName}</p>
                          </div>
                        )}
                        {bookingDetails.personalInfo.email && (
                          <div>
                            <p className="text-slate-500 text-[11px]">Email</p>
                            <p className="text-slate-900 mt-1 break-all">{bookingDetails.personalInfo.email}</p>
                          </div>
                        )}
                        {bookingDetails.personalInfo.phone && (
                          <div>
                            <p className="text-slate-500 text-[11px]">Phone</p>
                            <p className="text-slate-900 mt-1">{bookingDetails.personalInfo.phone}</p>
                          </div>
                        )}
                        {(bookingDetails.personalInfo.streetAddress || bookingDetails.personalInfo.address) && (
                          <div className="col-span-2">
                            <p className="text-slate-500 text-[11px]">Address</p>
                            <p className="text-slate-900 mt-1">
                              {bookingDetails.personalInfo.address?.street || bookingDetails.personalInfo.streetAddress}
                              {(bookingDetails.personalInfo.address?.city || bookingDetails.personalInfo.city) && `, ${bookingDetails.personalInfo.address?.city || bookingDetails.personalInfo.city}`}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Description */}
                  {bookingDetails.description && (
                    <div>
                      <h3 className="text-[13px] font-semibold text-slate-900 mb-2">Service Description</h3>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <p className="text-slate-700 text-[13px] leading-relaxed">{bookingDetails.description}</p>
                      </div>
                    </div>
                  )}

                  {/* Summary */}
                  <div className="bg-gray-50 rounded-md p-3">
                    <h3 className="text-[13px] font-semibold text-slate-900 mb-3">Booking Summary</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <p className="text-slate-500">Service Fee</p>
                        <p className="text-slate-900 font-semibold">{bookingDetails.amount}</p>
                      </div>
                      {bookingDetails.additionalServices && bookingDetails.additionalServices.length > 0 && (
                        <div className="flex justify-between">
                          <p className="text-slate-500">Additional Services</p>
                          <p className="text-slate-900 font-semibold">
                            ${bookingDetails.additionalServices.filter(s => s.price).reduce((sum, s) => sum + (s.price || 0), 0).toFixed(2)}
                          </p>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <p className="text-slate-500">Payment Method</p>
                        <p className="text-slate-900 font-semibold">{bookingDetails.paymentMethod || bookingDetails.payment}</p>
                      </div>
                      <div className="flex justify-between pt-2 border-t border-gray-200">
                        <p className="text-[13px] font-semibold text-slate-900">Total</p>
                        <p className="text-[13px] font-semibold text-indigo-700">{bookingDetails.amount}</p>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="pt-2">
                    <div className="flex flex-col sm:flex-row justify-between items-center gap-3">
                      <p className="text-slate-500 text-xs font-medium">Need help? <a href="#" className="text-indigo-700 hover:underline">Contact us</a></p>
                      <div className="flex gap-2 w-full sm:w-auto">
                        <button
                          onClick={() => selectedBooking && handleReschedule(selectedBooking)}
                          className="flex-1 sm:flex-none bg-yellow-600 hover:bg-yellow-700 text-white font-medium text-[13px] py-2 px-3 rounded-md cursor-pointer transition"
                        >
                          Reschedule
                        </button>
                        {/* <button className="flex-1 sm:flex-none bg-indigo-600 hover:bg-indigo-700 text-white font-medium text-[13px] py-2 px-3 rounded-md cursor-pointer transition">
                          Download Invoice
                        </button> */}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <p className="text-gray-600 text-sm">Failed to load booking details.</p>
                  <button
                    onClick={() => selectedBooking && handleViewDetails(selectedBooking)}
                    className="mt-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium text-[13px] py-2 px-3 rounded-md cursor-pointer transition"
                  >
                    Try Again
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default BookingList;
