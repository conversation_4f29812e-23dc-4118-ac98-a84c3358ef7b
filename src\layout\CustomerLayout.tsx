import AppHeader from '../feature-module/components/AppHeader';
import Footer from '../feature-module/components/Footer/Footer';
import Customers from '../feature-module/frontend/Customer/customers';
import CustomerSidebar from '../feature-module/frontend/Customer/customerSidebar';

import { useLocation } from 'react-router-dom';

const CustomerLayout = () => {
  const location = useLocation();
  const isHomePage = location.pathname === '/';
  return (
    <div className="flex flex-col min-h-screen bg-gray-50 text-slate-800">
      {/* Customer Sidebar (fixed on left, hidden on small screens) */}
      <div className="hidden md:block">
        <CustomerSidebar />
      </div>

      {/* {isHomePage ? (
        <div className="sticky top-0 w-full bg-white z-20 md:ml-[260px] ml-0 border-b border-gray-200">
          <AppHeader />
        </div>
      ) : (
        <div className="w-full md:ml-[260px] ml-0 bg-white border-b border-gray-200">
          <AppHeader />
        </div>
      )} */}

      <div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-14 py-6 md:ml-[260px] ml-0">
        <div className="w-full">
          <Customers />
        </div>
      </div>
      <div className="md:ml-[260px] ml-0 bg-white border-t border-gray-200"><Footer/></div>
    </div>
  );
};


export default CustomerLayout;
